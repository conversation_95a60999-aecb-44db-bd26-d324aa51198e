import React, { useState, useMemo, useEffect, useRef } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners,
  useDroppable,
  useDraggable,
} from '@dnd-kit/core';
import {
  Clock,
  Filter,
  ChevronLeft,
  ChevronRight,
  Pause,
  CheckCircle2,
  Archive,
  Plus,
  Calendar,
  ZoomIn,
  ZoomOut,
} from 'lucide-react';
import { Task } from '@/services/api';
import { useTaskStore } from '@/store/taskStore';
import { useProjectStore } from '@/store/projectStore';
import { useTagStore } from '@/store/tagStore';
import Button from '@/components/ui/Button';
import TagBadge from '@/components/Tags/TagBadge';
import CollapsibleTaskSection from './CollapsibleTaskSection';

interface TimelineProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

type ViewMode = '15min' | '30min' | '1hr' | '4hr' | '8hr' | 'daily';

// Helper function to format date as YYYY-MM-DD in local timezone
const formatLocalDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Generate time slots based on view mode
const generateTimeSlots = (date: Date, viewMode: ViewMode) => {
  const slots = [];
  const dateStr = formatLocalDate(date);

  switch (viewMode) {
    case '15min':
      for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 15) {
          const slotDate = new Date(date);
          slotDate.setHours(hour, minute, 0, 0);
          const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
          const ampm = hour < 12 ? 'AM' : 'PM';
          slots.push({
            id: `${dateStr}-${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
            label: `${displayHour}:${minute.toString().padStart(2, '0')} ${ampm}`,
            date: slotDate,
            hour,
            minute,
          });
        }
      }
      break;

    case '30min':
      for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          const slotDate = new Date(date);
          slotDate.setHours(hour, minute, 0, 0);
          const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
          const ampm = hour < 12 ? 'AM' : 'PM';
          slots.push({
            id: `${dateStr}-${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
            label: `${displayHour}:${minute.toString().padStart(2, '0')} ${ampm}`,
            date: slotDate,
            hour,
            minute,
          });
        }
      }
      break;

    case '1hr':
      for (let hour = 0; hour < 24; hour++) {
        const slotDate = new Date(date);
        slotDate.setHours(hour, 0, 0, 0);
        slots.push({
          id: `${dateStr}-${hour.toString().padStart(2, '0')}:00`,
          label: hour === 0 ? '12:00 AM' : hour < 12 ? `${hour}:00 AM` : hour === 12 ? '12:00 PM' : `${hour - 12}:00 PM`,
          date: slotDate,
          hour,
        });
      }
      break;

    case '4hr':
      for (let hour = 0; hour < 24; hour += 4) {
        const slotDate = new Date(date);
        slotDate.setHours(hour, 0, 0, 0);
        const endHour = Math.min(hour + 4, 24);
        slots.push({
          id: `${dateStr}-${hour.toString().padStart(2, '0')}:00`,
          label: `${hour.toString().padStart(2, '0')}:00-${endHour.toString().padStart(2, '0')}:00`,
          date: slotDate,
          hour,
          duration: 4,
        });
      }
      break;

    case '8hr':
      for (let hour = 0; hour < 24; hour += 8) {
        const slotDate = new Date(date);
        slotDate.setHours(hour, 0, 0, 0);
        const endHour = Math.min(hour + 8, 24);
        slots.push({
          id: `${dateStr}-${hour.toString().padStart(2, '0')}:00`,
          label: `${hour.toString().padStart(2, '0')}:00-${endHour.toString().padStart(2, '0')}:00`,
          date: slotDate,
          hour,
          duration: 8,
        });
      }
      break;

    case 'daily':
    default:
      const startOfWeek = new Date(date);
      startOfWeek.setDate(date.getDate() - date.getDay());
      for (let i = 0; i < 7; i++) {
        const slotDate = new Date(startOfWeek);
        slotDate.setDate(startOfWeek.getDate() + i);
        slots.push({
          id: formatLocalDate(slotDate),
          label: slotDate.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' }),
          date: slotDate,
          isToday: slotDate.toDateString() === new Date().toDateString(),
        });
      }
      break;
  }

  return slots;
};

// Beautiful Timeline Task Component
const TimelineTask: React.FC<{ task: Task }> = ({ task }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: task.id,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        transition-all duration-200 select-none
        ${isDragging ? 'opacity-80 rotate-1 scale-95 z-50 shadow-2xl' : ''}
      `}
    >
      <div
        {...attributes}
        {...listeners}
        className="
          relative p-4 rounded-xl border-2 cursor-grab active:cursor-grabbing
          bg-white hover:bg-gray-50 border-gray-200 hover:border-gray-300
          hover:shadow-lg transition-all duration-200 group select-none
          min-h-[80px] flex flex-col justify-between
        "
      >
        {/* Task Content */}
        <div className="relative z-10 pointer-events-none select-none">
          <div className="flex items-start justify-between mb-2">
            <h4 className="text-sm font-semibold text-gray-800 line-clamp-2 leading-tight">
              {task.title}
            </h4>
            {task.project && (
              <div
                className="w-3 h-3 rounded-full flex-shrink-0 ml-2 mt-0.5"
                style={{ backgroundColor: task.project.color || '#64748b' }}
              />
            )}
          </div>

          {task.content && (
            <p className="text-xs text-gray-600 line-clamp-2 mb-2">
              {task.content}
            </p>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {task.project && (
                <span className="text-xs text-gray-500 font-medium">
                  {task.project.name}
                </span>
              )}
              {task.tag && (
                <TagBadge tag={task.tag} size="sm" />
              )}
            </div>

            {task.scheduledTime && (
              <span className="text-xs text-gray-400">
                {new Date(task.scheduledTime).toLocaleTimeString('en-US', {
                  hour: 'numeric',
                  minute: '2-digit',
                })}
              </span>
            )}
          </div>
        </div>

        {/* Status indicator */}
        <div className={`
          absolute top-2 right-2 w-2 h-2 rounded-full
          ${task.status === 'IN_PROGRESS' ? 'bg-blue-500' :
            task.status === 'DONE' ? 'bg-green-500' :
            task.status === 'PAUSED' ? 'bg-yellow-500' : 'bg-gray-400'}
        `} />
      </div>
    </div>
  );
};

// Beautiful Timeline Slot Component
const TimelineSlot: React.FC<{
  slot: any;
  tasks: Task[];
  viewMode: ViewMode;
}> = ({ slot, tasks, viewMode }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: slot.id,
  });

  const isCurrentTime = () => {
    if (['15min', '30min', '1hr'].includes(viewMode)) {
      const now = new Date();
      if (viewMode === '15min' || viewMode === '30min') {
        return now.getHours() === slot.hour &&
               (slot.minute || 0) <= now.getMinutes() &&
               (slot.minute || 0) + (viewMode === '15min' ? 15 : 30) > now.getMinutes() &&
               slot.date.toDateString() === now.toDateString();
      } else {
        return slot.hour === now.getHours() && slot.date.toDateString() === now.toDateString();
      }
    } else {
      return slot.isToday;
    }
  };

  const getSlotWidth = () => {
    switch (viewMode) {
      case '15min':
      case '30min':
        return 200;
      case '1hr':
        return 250;
      case '4hr':
        return 300;
      case '8hr':
        return 350;
      case 'daily':
      default:
        return 280;
    }
  };

  const getSlotHeight = () => {
    return 500; // Fixed height, zoom is handled by container transform
  };

  return (
    <div
      ref={setNodeRef}
      className={`
        relative border-r border-gray-200 transition-all duration-200 flex-shrink-0
        bg-gradient-to-b from-gray-50 to-white
        ${isOver ? 'bg-blue-50 border-blue-300 shadow-inner' : 'hover:bg-gray-100'}
        ${isCurrentTime() ? 'bg-gradient-to-b from-blue-100 to-blue-50 border-blue-400' : ''}
      `}
      style={{
        width: `${getSlotWidth()}px`,
        height: `${getSlotHeight()}px`,
        minHeight: `${getSlotHeight()}px`,
      }}
    >
      {/* Time Label */}
      <div className={`
        sticky top-0 z-20 p-3 border-b border-gray-200 bg-white/90 backdrop-blur-sm
        ${isCurrentTime() ? 'bg-blue-100/90 border-blue-300' : ''}
      `}>
        <div className="text-sm font-semibold text-gray-700 text-center">
          {slot.label}
        </div>
        {isCurrentTime() && (
          <div className="text-xs text-blue-600 text-center mt-1 font-medium">
            Current Time
          </div>
        )}
      </div>

      {/* Current time indicator */}
      {isCurrentTime() && (
        <div className="absolute top-16 left-0 w-full h-1 bg-blue-500 z-10 shadow-sm" />
      )}

      {/* Tasks Container */}
      <div className="p-3 space-y-3 min-h-[400px]">
        {tasks.map((task) => (
          <TimelineTask key={task.id} task={task} />
        ))}

        {/* Drop indicator */}
        {isOver && tasks.length === 0 && (
          <div className="border-2 border-dashed border-blue-400 rounded-xl p-8 bg-blue-50/50">
            <div className="text-center">
              <Plus className="h-8 w-8 text-blue-400 mx-auto mb-2" />
              <p className="text-sm font-medium text-blue-600">Drop task here</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Main HorizontalTimeline Component
const HorizontalTimeline: React.FC<TimelineProps> = ({ currentDate, onDateChange }) => {
  const { tasks, updateTask } = useTaskStore();
  const { projects } = useProjectStore();
  const { tags } = useTagStore();

  // Core timeline state
  const [viewMode, setViewMode] = useState<ViewMode>('1hr');
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [selectedTag, setSelectedTag] = useState<string>('all');
  const [draggedTask, setDraggedTask] = useState<Task | null>(null);
  const [autoFollow, setAutoFollow] = useState<boolean>(true);

  // Zoom functionality
  const [zoomLevel, setZoomLevel] = useState(1);

  const timelineRef = useRef<HTMLDivElement>(null);

  // Configure sensors for perfect drag detection
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5, // Perfect balance for responsiveness
      },
    })
  );

  // Generate time slots based on view mode
  const timeSlots = useMemo(() => {
    return generateTimeSlots(currentDate, viewMode);
  }, [viewMode, currentDate]);

  // Auto-follow current time
  useEffect(() => {
    if (!autoFollow || !timelineRef.current) return;

    const scrollToCurrentTime = () => {
      const now = new Date();
      if (currentDate.toDateString() !== now.toDateString()) return;

      const timeSlotIndex = timeSlots.findIndex(slot => {
        if (['15min', '30min', '1hr'].includes(viewMode)) {
          if (viewMode === '15min' || viewMode === '30min') {
            return slot.hour === now.getHours() &&
                   (slot.minute || 0) <= now.getMinutes() &&
                   (slot.minute || 0) + (viewMode === '15min' ? 15 : 30) > now.getMinutes();
          } else {
            return slot.hour === now.getHours();
          }
        } else {
          return slot.isToday;
        }
      });

      if (timeSlotIndex >= 0 && timelineRef.current) {
        const slotWidth = viewMode === '15min' || viewMode === '30min' ? 200 :
                         viewMode === '1hr' ? 250 :
                         viewMode === '4hr' ? 300 :
                         viewMode === '8hr' ? 350 : 280;
        const scrollPosition = timeSlotIndex * slotWidth - (timelineRef.current.clientWidth / 2);
        timelineRef.current.scrollTo({
          left: Math.max(0, scrollPosition),
          behavior: 'smooth'
        });
      }
    };

    scrollToCurrentTime();
    const interval = setInterval(scrollToCurrentTime, 60000);
    return () => clearInterval(interval);
  }, [timeSlots, viewMode, currentDate, autoFollow]);

  // Filter tasks
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      if (selectedProject !== 'all' && task.projectId !== selectedProject) {
        return false;
      }
      if (selectedTag !== 'all' && task.tagId !== selectedTag) {
        return false;
      }
      return true;
    });
  }, [tasks, selectedProject, selectedTag]);

  // Get tasks for specific slot
  const getTasksForSlot = (slot: any) => {
    return filteredTasks.filter(task => {
      if (!task.scheduledTime) return false;
      if (!['TODO', 'IN_PROGRESS'].includes(task.status)) return false;

      const taskDate = new Date(task.scheduledTime);

      switch (viewMode) {
        case '15min':
        case '30min':
          return taskDate.getHours() === slot.hour &&
                 taskDate.getMinutes() === (slot.minute || 0) &&
                 taskDate.toDateString() === slot.date.toDateString();

        case '1hr':
          return taskDate.getHours() === slot.hour &&
                 taskDate.toDateString() === slot.date.toDateString();

        case '4hr':
        case '8hr':
          const slotStart = slot.hour;
          const slotEnd = slot.hour + (slot.duration || 1);
          return taskDate.getHours() >= slotStart &&
                 taskDate.getHours() < slotEnd &&
                 taskDate.toDateString() === slot.date.toDateString();

        case 'daily':
        default:
          return taskDate.toDateString() === slot.date.toDateString();
      }
    });
  };

  // Categorize tasks by status for sidebar
  const unscheduledTasks = filteredTasks.filter(task =>
    !task.scheduledTime && task.status === 'TODO'
  );

  const pausedTasks = filteredTasks.filter(task =>
    task.status === 'PAUSED'
  );

  const completedTasks = filteredTasks.filter(task =>
    task.status === 'DONE'
  );

  const archivedTasks = filteredTasks.filter(task =>
    task.status === 'ARCHIVED'
  );

  // Perfect drag start handler
  const handleDragStart = (event: DragStartEvent) => {
    const task = tasks.find(t => t.id === event.active.id);
    setDraggedTask(task || null);
  };

  // Flawless drag end handler with perfect date handling
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedTask(null);

    if (!over) return;

    const taskId = active.id as string;
    const dropZoneId = over.id as string;

    try {
      // Handle section drops - remove from timeline and update status
      if (dropZoneId === 'unscheduled-section') {
        await updateTask(taskId, {
          status: 'TODO',
          scheduledTime: undefined,
        });
        return;
      }

      if (dropZoneId === 'paused-section') {
        await updateTask(taskId, {
          status: 'PAUSED',
          scheduledTime: undefined,
        });
        return;
      }

      if (dropZoneId === 'completed-section') {
        await updateTask(taskId, {
          status: 'DONE',
          scheduledTime: undefined,
        });
        return;
      }

      if (dropZoneId === 'archived-section') {
        await updateTask(taskId, {
          status: 'ARCHIVED',
          scheduledTime: undefined,
        });
        return;
      }

      // Handle timeline slot drops with PERFECT date handling
      let scheduledTime: Date;

      if (['15min', '30min', '1hr'].includes(viewMode)) {
        const [dateStr, timeStr] = dropZoneId.split('-');
        const [hours, minutes = '0'] = timeStr.split(':');

        // Parse date components manually to avoid timezone issues
        const [year, month, day] = dateStr.split('-').map(Number);
        scheduledTime = new Date(year, month - 1, day, parseInt(hours), parseInt(minutes), 0, 0);
      } else {
        // For daily/multi-hour views
        const [year, month, day] = dropZoneId.split('-').map(Number);
        scheduledTime = new Date(year, month - 1, day, 9, 0, 0, 0);
      }

      await updateTask(taskId, {
        status: 'IN_PROGRESS',
        scheduledTime: scheduledTime.toISOString(),
      });
    } catch (error) {
      console.error('Failed to update task:', error);
    }
  };

  const navigateTime = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (['15min', '30min', '1hr', '4hr', '8hr'].includes(viewMode)) {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    } else {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    }
    onDateChange(newDate);
  };

  // Subtle zoom functionality with stable positioning
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();

    // Much more subtle zoom speed
    const zoomSpeed = 0.05;
    const delta = -e.deltaY; // Invert for natural zoom direction

    setZoomLevel(prevZoom => {
      const newZoom = Math.max(0.8, Math.min(1.5, prevZoom + (delta > 0 ? zoomSpeed : -zoomSpeed)));
      return newZoom;
    });
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="h-full flex flex-col bg-gradient-to-br from-gray-50 to-white">
        {/* Beautiful Timeline Header */}
        <div className="flex-shrink-0 p-6 border-b border-gray-200 bg-white shadow-sm">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Calendar className="h-6 w-6 mr-3 text-blue-600" />
              Timeline
            </h1>

            {/* Beautiful View Mode Toggle */}
            <div className="flex items-center space-x-2 bg-gray-100 rounded-xl p-1">
              {(['15min', '30min', '1hr', '4hr', '8hr', 'daily'] as ViewMode[]).map((mode) => (
                <Button
                  key={mode}
                  variant={viewMode === mode ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode(mode)}
                  className={`
                    transition-all duration-200 rounded-lg px-4 py-2
                    ${viewMode === mode
                      ? 'bg-white shadow-sm text-blue-600 font-semibold'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                    }
                  `}
                >
                  {mode}
                </Button>
              ))}
            </div>
          </div>

          {/* Navigation and Filters */}
          <div className="flex items-center justify-between">
            {/* Beautiful Date Navigation */}
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateTime('prev')}
                className="hover:bg-gray-100 rounded-lg p-2"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>

              <div className="text-center">
                <h2 className="text-lg font-semibold text-gray-900">
                  {['15min', '30min', '1hr', '4hr', '8hr'].includes(viewMode)
                    ? currentDate.toLocaleDateString('en-US', {
                        weekday: 'long',
                        month: 'long',
                        day: 'numeric',
                        year: 'numeric'
                      })
                    : `Week of ${timeSlots[0]?.label} - ${timeSlots[6]?.label}`
                  }
                </h2>
                <p className="text-sm text-gray-500">
                  {timeSlots.length} time slots
                </p>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateTime('next')}
                className="hover:bg-gray-100 rounded-lg p-2"
              >
                <ChevronRight className="h-5 w-5" />
              </Button>
            </div>

            {/* Beautiful Filters */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <select
                  value={selectedProject}
                  onChange={(e) => setSelectedProject(e.target.value)}
                  className="text-sm border border-gray-300 rounded-lg px-3 py-2 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Projects</option>
                  {projects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
              </div>

              <select
                value={selectedTag}
                onChange={(e) => setSelectedTag(e.target.value)}
                className="text-sm border border-gray-300 rounded-lg px-3 py-2 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Tags</option>
                {tags.map((tag) => (
                  <option key={tag.id} value={tag.id}>
                    {tag.name}
                  </option>
                ))}
              </select>

              <Button
                variant={autoFollow ? 'default' : 'outline'}
                size="sm"
                onClick={() => setAutoFollow(!autoFollow)}
                className="flex items-center space-x-2"
              >
                <Clock className="h-4 w-4" />
                <span>Follow</span>
              </Button>

              {/* Beautiful Zoom Controls */}
              <div className="flex items-center space-x-2 border-l border-gray-300 pl-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setZoomLevel(prev => Math.max(0.8, prev - 0.1))}
                  disabled={zoomLevel <= 0.8}
                  className="p-2"
                  title="Zoom Out"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>

                <div className="text-sm font-medium text-gray-700 min-w-[60px] text-center">
                  {Math.round(zoomLevel * 100)}%
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setZoomLevel(prev => Math.min(1.5, prev + 0.1))}
                  disabled={zoomLevel >= 1.5}
                  className="p-2"
                  title="Zoom In"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setZoomLevel(1)}
                  className="text-xs px-2"
                  title="Reset Zoom"
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Beautiful Timeline Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Beautiful Task Sections Sidebar */}
          <div className="w-80 flex-shrink-0 border-r border-gray-200 bg-gradient-to-b from-gray-50 to-white p-4 overflow-y-auto">
            <CollapsibleTaskSection
              id="unscheduled-section"
              title="Unscheduled Tasks"
              icon={<Clock className="h-4 w-4" />}
              tasks={unscheduledTasks}
              color="blue"
              description="Tasks waiting to be scheduled"
              defaultExpanded={true}
            />

            <CollapsibleTaskSection
              id="paused-section"
              title="Paused Tasks"
              icon={<Pause className="h-4 w-4" />}
              tasks={pausedTasks}
              color="yellow"
              description="Tasks temporarily paused"
              defaultExpanded={false}
            />

            <CollapsibleTaskSection
              id="completed-section"
              title="Completed Tasks"
              icon={<CheckCircle2 className="h-4 w-4" />}
              tasks={completedTasks}
              color="green"
              description="Finished tasks"
              defaultExpanded={false}
            />

            <CollapsibleTaskSection
              id="archived-section"
              title="Archived Tasks"
              icon={<Archive className="h-4 w-4" />}
              tasks={archivedTasks}
              color="gray"
              description="Archived tasks"
              defaultExpanded={false}
            />
          </div>

          {/* Beautiful Timeline Grid */}
          <div
            ref={timelineRef}
            className="flex-1 overflow-x-auto overflow-y-hidden bg-gradient-to-b from-white to-gray-50"
            onScroll={() => setAutoFollow(false)}
            onWheel={handleWheel}
            style={{
              height: 'calc(100vh - 200px)',
            }}
          >
            <div
              className="flex h-full min-w-max"
              style={{
                transform: `scale(${zoomLevel})`,
                transformOrigin: 'center center',
                transition: 'transform 0.1s ease-out',
              }}
            >
              {timeSlots.map((slot) => (
                <TimelineSlot
                  key={slot.id}
                  slot={slot}
                  tasks={getTasksForSlot(slot)}
                  viewMode={viewMode}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Beautiful Drag Overlay */}
        <DragOverlay>
          {draggedTask ? (
            <div className="transform rotate-3 scale-105">
              <TimelineTask task={draggedTask} />
            </div>
          ) : null}
        </DragOverlay>
      </div>
    </DndContext>
  );
};

export default HorizontalTimeline;