import React, { useState, useMemo, useEffect, useRef } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners,
  useDroppable,
  useDraggable,
} from '@dnd-kit/core';
import {
  Clock,
  Filter,
  ChevronLeft,
  ChevronRight,
  Pause,
  CheckCircle2,
  Archive,
} from 'lucide-react';
import { Task } from '@/services/api';
import { useTaskStore } from '@/store/taskStore';
import { useProjectStore } from '@/store/projectStore';
import { useTagStore } from '@/store/tagStore';
import Button from '@/components/ui/Button';
import TagBadge from '@/components/Tags/TagBadge';
import CollapsibleTaskSection from './CollapsibleTaskSection';

interface TimelineProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

type ViewMode = '15min' | '30min' | '1hr' | '8hr' | '12hr' | 'daily';

// Helper function to format date as YYYY-MM-DD in local timezone
const formatLocalDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Generate time slots based on view mode
const generateTimeSlots = (date: Date, viewMode: ViewMode) => {
  const slots = [];
  const dateStr = formatLocalDate(date); // Use local date formatting

  switch (viewMode) {
    case '15min':
      for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 15) {
          const slotDate = new Date(date);
          slotDate.setHours(hour, minute, 0, 0);
          const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
          const ampm = hour < 12 ? 'AM' : 'PM';
          slots.push({
            id: `${dateStr}-${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
            label: `${displayHour}:${minute.toString().padStart(2, '0')} ${ampm}`,
            date: slotDate,
            hour,
            minute,
          });
        }
      }
      break;

    case '30min':
      for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          const slotDate = new Date(date);
          slotDate.setHours(hour, minute, 0, 0);
          const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
          const ampm = hour < 12 ? 'AM' : 'PM';
          slots.push({
            id: `${dateStr}-${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
            label: `${displayHour}:${minute.toString().padStart(2, '0')} ${ampm}`,
            date: slotDate,
            hour,
            minute,
          });
        }
      }
      break;

    case '1hr':
      for (let hour = 0; hour < 24; hour++) {
        const slotDate = new Date(date);
        slotDate.setHours(hour, 0, 0, 0);
        slots.push({
          id: `${dateStr}-${hour.toString().padStart(2, '0')}:00`,
          label: hour === 0 ? '12:00 AM' : hour < 12 ? `${hour}:00 AM` : hour === 12 ? '12:00 PM' : `${hour - 12}:00 PM`,
          date: slotDate,
          hour,
        });
      }
      break;

    case '8hr':
      for (let hour = 0; hour < 24; hour += 8) {
        const slotDate = new Date(date);
        slotDate.setHours(hour, 0, 0, 0);
        const endHour = Math.min(hour + 8, 24);
        slots.push({
          id: `${dateStr}-${hour.toString().padStart(2, '0')}:00`,
          label: `${hour.toString().padStart(2, '0')}:00-${endHour.toString().padStart(2, '0')}:00`,
          date: slotDate,
          hour,
          duration: 8,
        });
      }
      break;

    case '12hr':
      for (let hour = 0; hour < 24; hour += 12) {
        const slotDate = new Date(date);
        slotDate.setHours(hour, 0, 0, 0);
        slots.push({
          id: `${dateStr}-${hour.toString().padStart(2, '0')}:00`,
          label: hour === 0 ? 'AM (00:00-12:00)' : 'PM (12:00-24:00)',
          date: slotDate,
          hour,
          duration: 12,
        });
      }
      break;

    case 'daily':
    default:
      const startOfWeek = new Date(date);
      startOfWeek.setDate(date.getDate() - date.getDay());
      for (let i = 0; i < 7; i++) {
        const slotDate = new Date(startOfWeek);
        slotDate.setDate(startOfWeek.getDate() + i);
        slots.push({
          id: formatLocalDate(slotDate), // Use local date formatting
          label: slotDate.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' }),
          date: slotDate,
          isToday: slotDate.toDateString() === new Date().toDateString(),
        });
      }
      break;
  }

  return slots;
};



// Draggable Timeline Task Component
const TimelineTask: React.FC<{
  task: Task;
}> = ({ task }) => {
  const { updateTask } = useTaskStore();
  const [isExtending, setIsExtending] = useState(false);

  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: task.id,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  // Simple duration extension handler
  const handleExtendDuration = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsExtending(true);

    try {
      const currentDuration = task.effortEstimate || 60;
      const newDuration = currentDuration + 30; // Add 30 minutes
      await updateTask(task.id, { effortEstimate: newDuration });
      console.log(`Extended task "${task.title}" duration to ${newDuration} minutes`);
    } catch (error) {
      console.error('Failed to extend task duration:', error);
    } finally {
      setIsExtending(false);
    }
  };

  const getStatusColor = () => {
    // All timeline tasks have clean white background
    return 'bg-white border-secondary-200 text-secondary-800';
  };

  const getStatusIndicatorColor = () => {
    // For scheduled TODO tasks, use project color if available
    if (task.status === 'TODO' && task.scheduledTime && task.project?.color) {
      return `bg-[${task.project.color}]`;
    }

    switch (task.status) {
      case 'IN_PROGRESS': return 'bg-blue-500';
      case 'DONE': return 'bg-green-500';
      case 'PAUSED': return 'bg-yellow-500';
      case 'ARCHIVED': return 'bg-gray-500';
      case 'TODO':
        // Distinguish between scheduled and unscheduled TODO tasks
        return task.scheduledTime
          ? 'bg-purple-500' // Scheduled (fallback if no project color)
          : 'bg-secondary-400'; // Unscheduled
      default: return 'bg-secondary-400';
    }
  };

  const getStatusTitle = () => {
    switch (task.status) {
      case 'IN_PROGRESS': return 'In Progress';
      case 'DONE': return 'Completed';
      case 'PAUSED': return 'Paused';
      case 'ARCHIVED': return 'Archived';
      case 'TODO':
        return task.scheduledTime ? 'Scheduled' : 'Unscheduled';
      default: return 'To Do';
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        transition-all select-none
        ${isDragging ? 'opacity-70 rotate-1 scale-95 z-50' : ''}
      `}
    >
      <div
        {...attributes}
        {...listeners}
        className={`
          relative p-2 rounded-lg border-2 cursor-grab active:cursor-grabbing
          hover:shadow-md transition-all group select-none draggable
          ${getStatusColor()}
          w-full h-full flex flex-col justify-center
        `}
      >
        {/* Task Content */}
        <div className="relative z-10 pointer-events-none select-none">
          <div className="flex items-center space-x-2 mb-1">
            {task.project && (
              <div
                className="w-2 h-2 rounded-full flex-shrink-0"
                style={{ backgroundColor: task.project.color || '#64748b' }}
              />
            )}
            <span className="text-xs font-medium truncate select-none">
              {task.title}
            </span>
          </div>

          {task.tag && (
            <div className="mt-1 select-none">
              <TagBadge tag={task.tag} size="sm" />
            </div>
          )}
        </div>

        {/* Status Indicator */}
        <div className="absolute top-1 right-1">
          <div
            className={`w-2 h-2 rounded-full ${
              task.status === 'TODO' && task.scheduledTime && task.project?.color
                ? ''
                : getStatusIndicatorColor()
            }`}
            style={
              task.status === 'TODO' && task.scheduledTime && task.project?.color
                ? { backgroundColor: task.project.color }
                : {}
            }
            title={getStatusTitle()}
          />
        </div>

        {/* Duration Extension Handle */}
        <div
          className={`absolute right-0 top-1/2 transform -translate-y-1/2 w-2 h-6 bg-primary-500 cursor-pointer opacity-0 group-hover:opacity-75 transition-opacity pointer-events-auto rounded-sm ${isExtending ? 'opacity-100 bg-primary-600' : ''}`}
          onClick={handleExtendDuration}
          title="Click to extend task duration by 30 minutes"
        />
      </div>
    </div>
  );
};

// Large Timeline Slot with Full-Height Project Lanes
const TimelineSlot: React.FC<{
  slot: any;
  tasks: Task[];
  viewMode: ViewMode;
}> = ({ slot, tasks, viewMode }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: slot.id,
  });

  const isCurrentTime = () => {
    if (['15min', '30min', '1hr'].includes(viewMode)) {
      const now = new Date();
      if (viewMode === '15min' || viewMode === '30min') {
        return now.getHours() === slot.hour &&
               (slot.minute || 0) <= now.getMinutes() &&
               (slot.minute || 0) + (viewMode === '15min' ? 15 : 30) > now.getMinutes() &&
               slot.date.toDateString() === now.toDateString();
      } else {
        return slot.hour === now.getHours() && slot.date.toDateString() === now.toDateString();
      }
    } else {
      return slot.isToday;
    }
  };



  // Calculate width based on view mode
  const getSlotWidth = () => {
    switch (viewMode) {
      case '15min':
      case '30min':
        return 180;
      case '1hr':
        return 220;
      case '8hr':
      case '12hr':
        return 280;
      case 'daily':
      default:
        return 200;
    }
  };

  return (
    <div
      ref={setNodeRef}
      className={`
        relative border-r border-secondary-200 transition-all flex-shrink-0
        ${isOver ? 'bg-primary-50 border-primary-300' : 'hover:bg-secondary-50'}
        ${isCurrentTime() ? 'bg-blue-50 border-blue-300' : ''}
      `}
      style={{
        width: `${getSlotWidth()}px`,
        height: '100%',
        minHeight: '600px',
      }}
    >
      {/* Time Label */}
      <div className="absolute top-2 left-2 text-sm font-semibold text-secondary-700 z-10">
        {slot.label}
      </div>

      {/* Current Time Indicator */}
      {isCurrentTime() && (
        <div className="absolute top-0 left-0 w-full h-2 bg-blue-500 z-10" />
      )}

      {/* Tasks Container - Improved for Multiple Tasks */}
      <div className="pt-12 pb-4 px-2 h-full flex flex-col space-y-2 overflow-y-auto">
        {tasks.map((task) => {
          const totalTasks = tasks.length;
          // Better height calculation for multiple tasks
          const availableHeight = 600 - 80; // Total height minus padding
          const minTaskHeight = 60; // Minimum height per task
          const maxTaskHeight = totalTasks === 1 ? 150 : 100; // Larger if single task
          const calculatedHeight = totalTasks > 0
            ? Math.max(minTaskHeight, Math.min(maxTaskHeight, availableHeight / totalTasks))
            : minTaskHeight;

          return (
            <div
              key={task.id}
              className="relative flex-shrink-0 bg-white rounded-lg border border-gray-200 shadow-sm"
              style={{
                minHeight: `${calculatedHeight}px`,
                height: `${calculatedHeight}px`,
              }}
            >
              <TimelineTask
                task={task}
              />
            </div>
          );
        })}
      </div>

      {/* Drop Indicator */}
      {isOver && (
        <div className="absolute inset-3 border-2 border-dashed border-primary-400 rounded-lg bg-primary-50 flex items-center justify-center z-20">
          <span className="text-sm text-primary-600 font-medium">Drop here</span>
        </div>
      )}
    </div>
  );
};

const HorizontalTimeline: React.FC<TimelineProps> = ({ currentDate, onDateChange }) => {
  const { tasks, updateTask } = useTaskStore();
  const { projects } = useProjectStore();
  const { tags } = useTagStore();
  const [viewMode, setViewMode] = useState<ViewMode>('1hr');
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [selectedTag, setSelectedTag] = useState<string>('all');
  const [draggedTask, setDraggedTask] = useState<Task | null>(null);
  const [autoFollow, setAutoFollow] = useState<boolean>(true);
  const timelineRef = useRef<HTMLDivElement>(null);
  const [isRightDragging, setIsRightDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, scrollLeft: 0 });

  // Auto-archive past tasks (only tasks that are more than 1 hour old)
  useEffect(() => {
    const archivePastTasks = async () => {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000); // 1 hour ago

      const pastTasks = tasks.filter(task => {
        if (!task.scheduledTime || task.status === 'ARCHIVED') return false;
        const taskTime = new Date(task.scheduledTime);
        // Only archive tasks that are more than 1 hour old
        return taskTime < oneHourAgo && ['TODO', 'IN_PROGRESS'].includes(task.status);
      });

      for (const task of pastTasks) {
        try {
          await updateTask(task.id, {
            status: 'ARCHIVED',
            scheduledTime: undefined,
          });
        } catch (error) {
          console.error('Failed to archive past task:', error);
        }
      }
    };

    // Run every 10 minutes instead of every minute
    const interval = setInterval(archivePastTasks, 600000);
    return () => clearInterval(interval);
  }, [tasks, updateTask]);



  // Configure sensors for drag detection with improved settings
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3, // Reduced distance for more responsive dragging
      },
    })
  );

  // Generate time slots based on view mode
  const timeSlots = useMemo(() => {
    return generateTimeSlots(currentDate, viewMode);
  }, [viewMode, currentDate]);

  // Auto-follow current time
  useEffect(() => {
    if (!autoFollow || !timelineRef.current) return;

    const scrollToCurrentTime = () => {
      const now = new Date();

      // Only auto-follow if viewing today
      if (currentDate.toDateString() !== now.toDateString()) return;

      const timeSlotIndex = timeSlots.findIndex(slot => {
        if (['15min', '30min', '1hr'].includes(viewMode)) {
          if (viewMode === '15min' || viewMode === '30min') {
            return slot.hour === now.getHours() &&
                   (slot.minute || 0) <= now.getMinutes() &&
                   (slot.minute || 0) + (viewMode === '15min' ? 15 : 30) > now.getMinutes();
          } else {
            return slot.hour === now.getHours();
          }
        } else {
          return slot.isToday;
        }
      });

      if (timeSlotIndex >= 0 && timelineRef.current) {
        const slotWidth = ['15min', '30min', '1hr'].includes(viewMode) ? 150 : 120;
        const scrollPosition = timeSlotIndex * slotWidth - (timelineRef.current.clientWidth / 2);
        timelineRef.current.scrollTo({
          left: Math.max(0, scrollPosition),
          behavior: 'smooth'
        });
      }
    };

    // Scroll immediately and then every minute
    scrollToCurrentTime();
    const interval = setInterval(scrollToCurrentTime, 60000);
    return () => clearInterval(interval);
  }, [timeSlots, viewMode, currentDate, autoFollow]);

  // Filter tasks
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      if (selectedProject !== 'all' && task.projectId !== selectedProject) {
        return false;
      }
      if (selectedTag !== 'all' && task.tagId !== selectedTag) {
        return false;
      }
      return true;
    });
  }, [tasks, selectedProject, selectedTag]);

  // Get tasks for specific slot (only show scheduled and in-progress tasks)
  const getTasksForSlot = (slot: any) => {
    return filteredTasks.filter(task => {
      if (!task.scheduledTime) return false;

      // Only show tasks that are scheduled or in progress on the timeline
      if (!['TODO', 'IN_PROGRESS'].includes(task.status)) return false;

      const taskDate = new Date(task.scheduledTime);

      switch (viewMode) {
        case '15min':
        case '30min':
          return taskDate.getHours() === slot.hour &&
                 taskDate.getMinutes() === (slot.minute || 0) &&
                 taskDate.toDateString() === slot.date.toDateString();

        case '1hr':
          return taskDate.getHours() === slot.hour &&
                 taskDate.toDateString() === slot.date.toDateString();

        case '8hr':
        case '12hr':
          const slotStart = slot.hour;
          const slotEnd = slot.hour + (slot.duration || 1);
          return taskDate.getHours() >= slotStart &&
                 taskDate.getHours() < slotEnd &&
                 taskDate.toDateString() === slot.date.toDateString();

        case 'daily':
        default:
          return taskDate.toDateString() === slot.date.toDateString();
      }
    });
  };

  // Categorize tasks by status
  const unscheduledTasks = filteredTasks.filter(task =>
    !task.scheduledTime && task.status === 'TODO'
  );

  const pausedTasks = filteredTasks.filter(task =>
    task.status === 'PAUSED'
  );

  const completedTasks = filteredTasks.filter(task =>
    task.status === 'DONE'
  );

  const archivedTasks = filteredTasks.filter(task =>
    task.status === 'ARCHIVED'
  );

  const handleDragStart = (event: DragStartEvent) => {
    const task = tasks.find(t => t.id === event.active.id);
    setDraggedTask(task || null);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedTask(null);

    if (!over) return;

    const taskId = active.id as string;
    const dropZoneId = over.id as string;

    try {
      // Handle section drops - remove from timeline and update status
      if (dropZoneId === 'unscheduled-section') {
        await updateTask(taskId, {
          status: 'TODO',
          scheduledTime: '', // Remove from timeline (empty string to clear)
        });
        return;
      }

      if (dropZoneId === 'paused-section') {
        await updateTask(taskId, {
          status: 'PAUSED',
          scheduledTime: undefined, // Remove from timeline
        });
        return;
      }

      if (dropZoneId === 'completed-section') {
        await updateTask(taskId, {
          status: 'DONE',
          scheduledTime: undefined, // Remove from timeline
        });
        return;
      }

      if (dropZoneId === 'archived-section') {
        await updateTask(taskId, {
          status: 'ARCHIVED',
          scheduledTime: undefined, // Remove from timeline
        });
        return;
      }

      // Handle timeline slot drops (including project lanes)
      let scheduledTime: Date;
      let projectId: string | undefined;

      // Check if this is a project lane drop (format: slot-id-project-id)
      const dropParts = dropZoneId.split('-');
      if (dropParts.length > 3) {
        // Project lane drop
        const slotId = dropParts.slice(0, -1).join('-');
        projectId = dropParts[dropParts.length - 1];

        if (['15min', '30min', '1hr'].includes(viewMode)) {
          const [dateStr, timeStr] = slotId.split('-');
          const [hours, minutes = '0'] = timeStr.split(':');
          // Parse date components manually to avoid timezone issues
          const [year, month, day] = dateStr.split('-').map(Number);
          scheduledTime = new Date(year, month - 1, day, parseInt(hours), parseInt(minutes), 0, 0);
        } else {
          // Parse date components manually to avoid timezone issues
          const [year, month, day] = slotId.split('-').map(Number);
          scheduledTime = new Date(year, month - 1, day, 9, 0, 0, 0);
        }
      } else {
        // Regular slot drop
        if (['15min', '30min', '1hr'].includes(viewMode)) {
          const [dateStr, timeStr] = dropZoneId.split('-');
          const [hours, minutes = '0'] = timeStr.split(':');
          // Parse date components manually to avoid timezone issues
          const [year, month, day] = dateStr.split('-').map(Number);
          scheduledTime = new Date(year, month - 1, day, parseInt(hours), parseInt(minutes), 0, 0);
        } else {
          // Parse date components manually to avoid timezone issues
          const [year, month, day] = dropZoneId.split('-').map(Number);
          scheduledTime = new Date(year, month - 1, day, 9, 0, 0, 0);
        }
      }

      const updateData: any = {
        status: 'IN_PROGRESS', // Set to IN_PROGRESS when dropped on timeline
        scheduledTime: scheduledTime.toISOString(),
      };

      // Set project if dropped on project lane
      if (projectId && projectId !== 'no-project') {
        updateData.projectId = projectId;
      } else if (projectId === 'no-project') {
        updateData.projectId = null;
      }

      await updateTask(taskId, updateData);
    } catch (error) {
      console.error('Failed to update task:', error);
    }
  };





  const navigateTime = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (['15min', '30min', '1hr', '8hr', '12hr'].includes(viewMode)) {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    } else {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    }
    onDateChange(newDate);
  };

  // Simple zoom functionality - just switch between time modes
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault(); // Prevent default scrolling

    const delta = e.deltaY;

    if (delta < 0) {
      // Scroll up = zoom in (smaller time intervals)
      if (viewMode === '1hr') setViewMode('30min');
      else if (viewMode === '30min') setViewMode('15min');
      else if (viewMode === '8hr') setViewMode('1hr');
      else if (viewMode === '12hr') setViewMode('8hr');
      else if (viewMode === 'daily') setViewMode('12hr');
    } else {
      // Scroll down = zoom out (larger time intervals)
      if (viewMode === '15min') setViewMode('30min');
      else if (viewMode === '30min') setViewMode('1hr');
      else if (viewMode === '1hr') setViewMode('8hr');
      else if (viewMode === '8hr') setViewMode('12hr');
      else if (viewMode === '12hr') setViewMode('daily');
    }
  };

  // Right-click drag for horizontal scrolling
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button === 2 && timelineRef.current) { // Right mouse button
      e.preventDefault();
      setIsRightDragging(true);
      setDragStart({
        x: e.clientX,
        scrollLeft: timelineRef.current.scrollLeft
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isRightDragging || !timelineRef.current) return;
    e.preventDefault();
    const deltaX = e.clientX - dragStart.x;
    timelineRef.current.scrollLeft = dragStart.scrollLeft - deltaX;
  };

  const handleMouseUp = () => {
    setIsRightDragging(false);
  };

  // Prevent context menu on right-click
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="h-full flex flex-col dnd-context">
        {/* Timeline Header */}
        <div className="flex-shrink-0 p-4 border-b border-secondary-200 bg-white">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-secondary-900 flex items-center">
              <Clock className="h-5 w-5 mr-2 text-primary-600" />
              Timeline View
            </h2>

            {/* View Mode Toggle */}
            <div className="flex items-center space-x-1">
              <Button
                variant={viewMode === '15min' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('15min')}
              >
                15min
              </Button>
              <Button
                variant={viewMode === '30min' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('30min')}
              >
                30min
              </Button>
              <Button
                variant={viewMode === '1hr' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('1hr')}
              >
                1hr
              </Button>
              <Button
                variant={viewMode === '8hr' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('8hr')}
              >
                8hr
              </Button>
              <Button
                variant={viewMode === '12hr' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('12hr')}
              >
                12hr
              </Button>
              <Button
                variant={viewMode === 'daily' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('daily')}
              >
                Daily
              </Button>

              {/* Auto-follow toggle */}
              <div className="ml-4 border-l border-secondary-300 pl-4">
                <Button
                  variant={autoFollow ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setAutoFollow(!autoFollow)}
                  title="Auto-follow current time"
                >
                  <Clock className="h-4 w-4 mr-1" />
                  Follow
                </Button>
              </div>
            </div>
          </div>

          {/* Navigation and Filters */}
          <div className="flex items-center justify-between">
            {/* Date Navigation */}
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" onClick={() => navigateTime('prev')}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm font-medium text-secondary-700 min-w-[200px] text-center">
                {['15min', '30min', '1hr', '8hr', '12hr'].includes(viewMode)
                  ? currentDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' })
                  : `Week of ${timeSlots[0]?.label} - ${timeSlots[6]?.label}`
                }
              </span>
              <Button variant="ghost" size="sm" onClick={() => navigateTime('next')}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Filters */}
            <div className="flex items-center space-x-4">
              {/* Project Filter */}
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-secondary-600" />
                <select
                  value={selectedProject}
                  onChange={(e) => setSelectedProject(e.target.value)}
                  className="text-sm border border-secondary-300 rounded px-2 py-1"
                >
                  <option value="all">All Projects</option>
                  {projects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Tag Filter */}
              <select
                value={selectedTag}
                onChange={(e) => setSelectedTag(e.target.value)}
                className="text-sm border border-secondary-300 rounded px-2 py-1"
              >
                <option value="all">All Tags</option>
                {tags.map((tag) => (
                  <option key={tag.id} value={tag.id}>
                    {tag.name}
                  </option>
                ))}
              </select>


            </div>
          </div>
        </div>

        {/* Timeline Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Task Sections Sidebar */}
          <div className="w-80 flex-shrink-0 border-r border-secondary-200 bg-secondary-50 p-4 overflow-y-auto">
            {/* Unscheduled Tasks */}
            <CollapsibleTaskSection
              id="unscheduled-section"
              title="Unscheduled Tasks"
              icon={<Clock className="h-4 w-4" />}
              tasks={unscheduledTasks}
              color="blue"
              description="Tasks waiting to be scheduled"
              defaultExpanded={true}
            />

            {/* Paused Tasks */}
            <CollapsibleTaskSection
              id="paused-section"
              title="Paused Tasks"
              icon={<Pause className="h-4 w-4" />}
              tasks={pausedTasks}
              color="yellow"
              description="Tasks temporarily paused"
              defaultExpanded={false}
            />

            {/* Completed Tasks */}
            <CollapsibleTaskSection
              id="completed-section"
              title="Completed Tasks"
              icon={<CheckCircle2 className="h-4 w-4" />}
              tasks={completedTasks}
              color="green"
              description="Finished tasks"
              defaultExpanded={false}
            />

            {/* Archived Tasks */}
            <CollapsibleTaskSection
              id="archived-section"
              title="Archived Tasks"
              icon={<Archive className="h-4 w-4" />}
              tasks={archivedTasks}
              color="gray"
              description="Archived tasks"
              defaultExpanded={false}
            />
          </div>

          {/* Timeline Grid */}
          <div
            ref={timelineRef}
            className="flex-1 overflow-x-auto overflow-y-hidden"
            onScroll={() => setAutoFollow(false)} // Disable auto-follow when user scrolls
            onWheel={handleWheel}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onContextMenu={handleContextMenu}
            style={{
              height: 'calc(100vh - 180px)', // Increased height
              cursor: isRightDragging ? 'grabbing' : 'default'
            }}
          >
            <div className="flex h-full min-w-max">
              {timeSlots.map((slot) => (
                <TimelineSlot
                  key={slot.id}
                  slot={slot}
                  tasks={getTasksForSlot(slot)}
                  viewMode={viewMode}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Drag Overlay */}
        <DragOverlay>
          {draggedTask ? (
            <TimelineTask
              task={draggedTask}
            />
          ) : null}
        </DragOverlay>
      </div>
    </DndContext>
  );
};

export default HorizontalTimeline;
