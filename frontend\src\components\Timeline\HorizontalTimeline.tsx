import React, { useState, useMemo, useEffect, useRef } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners,
  useDroppable,
  useDraggable,
} from '@dnd-kit/core';
import {
  Clock,
  Filter,
  ChevronLeft,
  ChevronRight,
  Pause,
  CheckCircle2,
  Archive,
  Plus,
  Calendar,
  ZoomIn,
  ZoomOut,
} from 'lucide-react';
import { Task } from '@/services/api';
import { useTaskStore } from '@/store/taskStore';
import { useProjectStore } from '@/store/projectStore';
import { useTagStore } from '@/store/tagStore';
import Button from '@/components/ui/Button';
import TagBadge from '@/components/Tags/TagBadge';
import CollapsibleTaskSection from './CollapsibleTaskSection';

interface TimelineProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

type ViewMode = '15min' | '30min' | '1hr' | '4hr' | '8hr' | 'daily';

// Helper function to format date as YYYY-MM-DD in local timezone
const formatLocalDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Generate time slots based on view mode
const generateTimeSlots = (date: Date, viewMode: ViewMode) => {
  const slots = [];
  const dateStr = formatLocalDate(date);

  switch (viewMode) {
    case '15min':
      for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 15) {
          const slotDate = new Date(date);
          slotDate.setHours(hour, minute, 0, 0);
          const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
          const ampm = hour < 12 ? 'AM' : 'PM';
          slots.push({
            id: `${dateStr}-${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
            label: `${displayHour}:${minute.toString().padStart(2, '0')} ${ampm}`,
            date: slotDate,
            hour,
            minute,
          });
        }
      }
      break;

    case '30min':
      for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          const slotDate = new Date(date);
          slotDate.setHours(hour, minute, 0, 0);
          const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
          const ampm = hour < 12 ? 'AM' : 'PM';
          slots.push({
            id: `${dateStr}-${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
            label: `${displayHour}:${minute.toString().padStart(2, '0')} ${ampm}`,
            date: slotDate,
            hour,
            minute,
          });
        }
      }
      break;

    case '1hr':
      for (let hour = 0; hour < 24; hour++) {
        const slotDate = new Date(date);
        slotDate.setHours(hour, 0, 0, 0);
        slots.push({
          id: `${dateStr}-${hour.toString().padStart(2, '0')}:00`,
          label: hour === 0 ? '12:00 AM' : hour < 12 ? `${hour}:00 AM` : hour === 12 ? '12:00 PM' : `${hour - 12}:00 PM`,
          date: slotDate,
          hour,
        });
      }
      break;

    case '4hr':
      for (let hour = 0; hour < 24; hour += 4) {
        const slotDate = new Date(date);
        slotDate.setHours(hour, 0, 0, 0);
        const endHour = Math.min(hour + 4, 24);
        slots.push({
          id: `${dateStr}-${hour.toString().padStart(2, '0')}:00`,
          label: `${hour.toString().padStart(2, '0')}:00-${endHour.toString().padStart(2, '0')}:00`,
          date: slotDate,
          hour,
          duration: 4,
        });
      }
      break;

    case '8hr':
      for (let hour = 0; hour < 24; hour += 8) {
        const slotDate = new Date(date);
        slotDate.setHours(hour, 0, 0, 0);
        const endHour = Math.min(hour + 8, 24);
        slots.push({
          id: `${dateStr}-${hour.toString().padStart(2, '0')}:00`,
          label: `${hour.toString().padStart(2, '0')}:00-${endHour.toString().padStart(2, '0')}:00`,
          date: slotDate,
          hour,
          duration: 8,
        });
      }
      break;

    case 'daily':
    default:
      const startOfWeek = new Date(date);
      startOfWeek.setDate(date.getDate() - date.getDay());
      for (let i = 0; i < 7; i++) {
        const slotDate = new Date(startOfWeek);
        slotDate.setDate(startOfWeek.getDate() + i);
        slots.push({
          id: formatLocalDate(slotDate),
          label: slotDate.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' }),
          date: slotDate,
          isToday: slotDate.toDateString() === new Date().toDateString(),
        });
      }
      break;
  }

  return slots;
};

// Beautiful Timeline Task Component with Duration Support
const TimelineTask: React.FC<{ task: Task; textZoom: number; viewMode: ViewMode }> = ({ task, textZoom, viewMode }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: task.id,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  // Calculate task width based on duration and view mode
  const getTaskWidth = () => {
    const duration = task.effortEstimate || 60; // Default 1 hour in minutes

    switch (viewMode) {
      case '15min':
        return Math.max(1, Math.ceil(duration / 15)) * (200 * textZoom) - 8; // Account for gaps
      case '30min':
        return Math.max(1, Math.ceil(duration / 30)) * (200 * textZoom) - 8;
      case '1hr':
        return Math.max(1, Math.ceil(duration / 60)) * (250 * textZoom) - 8;
      case '4hr':
        return Math.max(1, Math.ceil(duration / 240)) * (300 * textZoom) - 8;
      case '8hr':
        return Math.max(1, Math.ceil(duration / 480)) * (350 * textZoom) - 8;
      default:
        return 200 * textZoom - 8;
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        transition-all duration-200 select-none
        ${isDragging ? 'opacity-80 rotate-1 scale-95 z-50 shadow-2xl' : ''}
      `}
    >
      <div
        {...attributes}
        {...listeners}
        className="
          relative p-4 rounded-xl border-2 cursor-grab active:cursor-grabbing
          bg-white hover:bg-gray-50 border-gray-200 hover:border-gray-300
          hover:shadow-lg transition-all duration-200 group select-none
          min-h-[80px] flex flex-col justify-between
        "
      >
        {/* Task Content */}
        <div className="relative z-10 pointer-events-none select-none">
          <div className="flex items-start justify-between mb-2">
            <h4
              className="font-semibold text-gray-800 line-clamp-2 leading-tight"
              style={{ fontSize: `${0.875 * textZoom}rem` }}
            >
              {task.title}
            </h4>
            {task.project && (
              <div
                className="w-3 h-3 rounded-full flex-shrink-0 ml-2 mt-0.5"
                style={{ backgroundColor: task.project.color || '#64748b' }}
              />
            )}
          </div>

          {task.content && (
            <p className="text-xs text-gray-600 line-clamp-2 mb-2">
              {task.content}
            </p>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {task.project && (
                <span className="text-xs text-gray-500 font-medium">
                  {task.project.name}
                </span>
              )}
              {task.tag && (
                <TagBadge tag={task.tag} size="sm" />
              )}
            </div>

            {task.scheduledTime && (
              <span className="text-xs text-gray-400">
                {new Date(task.scheduledTime).toLocaleTimeString('en-US', {
                  hour: 'numeric',
                  minute: '2-digit',
                })}
              </span>
            )}
          </div>
        </div>

        {/* Status indicator */}
        <div className={`
          absolute top-2 right-2 w-2 h-2 rounded-full
          ${task.status === 'IN_PROGRESS' ? 'bg-blue-500' :
            task.status === 'DONE' ? 'bg-green-500' :
            task.status === 'PAUSED' ? 'bg-yellow-500' : 'bg-gray-400'}
        `} />
      </div>
    </div>
  );
};

// Beautiful Timeline Slot Component
const TimelineSlot: React.FC<{
  slot: any;
  tasks: Task[];
  viewMode: ViewMode;
  textZoom: number;
}> = ({ slot, tasks, viewMode, textZoom }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: slot.id,
  });

  const isCurrentTime = () => {
    if (['15min', '30min', '1hr'].includes(viewMode)) {
      const now = new Date();
      if (viewMode === '15min' || viewMode === '30min') {
        return now.getHours() === slot.hour &&
               (slot.minute || 0) <= now.getMinutes() &&
               (slot.minute || 0) + (viewMode === '15min' ? 15 : 30) > now.getMinutes() &&
               slot.date.toDateString() === now.toDateString();
      } else {
        return slot.hour === now.getHours() && slot.date.toDateString() === now.toDateString();
      }
    } else {
      return slot.isToday;
    }
  };

  const isPastTime = () => {
    const now = new Date();
    if (['15min', '30min', '1hr'].includes(viewMode)) {
      if (viewMode === '15min' || viewMode === '30min') {
        const slotEndTime = new Date(slot.date);
        slotEndTime.setHours(slot.hour, (slot.minute || 0) + (viewMode === '15min' ? 15 : 30));
        return slotEndTime < now;
      } else {
        const slotEndTime = new Date(slot.date);
        slotEndTime.setHours(slot.hour + 1, 0);
        return slotEndTime < now;
      }
    } else {
      return slot.date.toDateString() < now.toDateString();
    }
  };

  const getSlotWidth = () => {
    let baseWidth;
    switch (viewMode) {
      case '15min':
      case '30min':
        baseWidth = 200;
        break;
      case '1hr':
        baseWidth = 250;
        break;
      case '4hr':
        baseWidth = 300;
        break;
      case '8hr':
        baseWidth = 350;
        break;
      case 'daily':
      default:
        baseWidth = 280;
        break;
    }
    return baseWidth * textZoom; // Apply text zoom to horizontal spacing
  };

  const getSlotHeight = () => {
    return 500; // Fixed height, zoom is handled by container transform
  };

  return (
    <div
      ref={setNodeRef}
      className={`
        relative border-r border-gray-200 transition-all duration-200 flex-shrink-0
        bg-gradient-to-b from-gray-50 to-white
        ${isOver ? 'bg-blue-50 border-blue-300 shadow-inner' : 'hover:bg-gray-100'}
        ${isCurrentTime() ? 'bg-gradient-to-b from-blue-100 to-blue-50 border-blue-400' : ''}
      `}
      style={{
        width: `${getSlotWidth()}px`,
        height: `${getSlotHeight()}px`,
        minHeight: `${getSlotHeight()}px`,
      }}
    >
      {/* Time Label - Fixed position with text zoom */}
      <div className={`
        absolute top-0 left-0 right-0 z-30 p-3 border-b border-gray-200 bg-white/95 backdrop-blur-sm
        ${isCurrentTime() ? 'bg-blue-100/95 border-blue-300' : ''}
        ${isPastTime() ? 'bg-gray-100/95 text-gray-400' : ''}
      `}>
        <div
          className={`font-semibold text-center ${isPastTime() ? 'text-gray-400' : 'text-gray-700'}`}
          style={{ fontSize: `${0.875 * textZoom}rem` }}
        >
          {slot.label}
        </div>
        {isCurrentTime() && (
          <div
            className="text-blue-600 text-center mt-1 font-medium"
            style={{ fontSize: `${0.75 * textZoom}rem` }}
          >
            Current Time
          </div>
        )}
      </div>

      {/* Current time indicator */}
      {isCurrentTime() && (
        <div className="absolute top-16 left-0 w-full h-1 bg-blue-500 z-10 shadow-sm" />
      )}

      {/* Tasks Container */}
      <div className="p-3 space-y-3 min-h-[400px] mt-16">
        {tasks.map((task) => (
          <TimelineTask key={task.id} task={task} textZoom={textZoom} />
        ))}

        {/* Drop indicator */}
        {isOver && tasks.length === 0 && (
          <div className="border-2 border-dashed border-blue-400 rounded-xl p-8 bg-blue-50/50">
            <div className="text-center">
              <Plus className="h-8 w-8 text-blue-400 mx-auto mb-2" />
              <p className="text-sm font-medium text-blue-600">Drop task here</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Main HorizontalTimeline Component
const HorizontalTimeline: React.FC<TimelineProps> = ({ currentDate, onDateChange }) => {
  const { tasks, updateTask } = useTaskStore();
  const { projects } = useProjectStore();
  const { tags } = useTagStore();

  // Core timeline state
  const [viewMode, setViewMode] = useState<ViewMode>('1hr');
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [selectedTag, setSelectedTag] = useState<string>('all');
  const [draggedTask, setDraggedTask] = useState<Task | null>(null);
  const [autoFollow, setAutoFollow] = useState<boolean>(true);

  // Text and spacing zoom functionality
  const [textZoom, setTextZoom] = useState(1);

  // Pan functionality
  const [isPanning, setIsPanning] = useState(false);
  const [panStart, setPanStart] = useState({ x: 0, scrollLeft: 0 });

  const timelineRef = useRef<HTMLDivElement>(null);

  // Configure sensors for perfect drag detection
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5, // Perfect balance for responsiveness
      },
    })
  );

  // Generate time slots based on view mode
  const timeSlots = useMemo(() => {
    return generateTimeSlots(currentDate, viewMode);
  }, [viewMode, currentDate]);

  // Auto-follow current time
  useEffect(() => {
    if (!autoFollow || !timelineRef.current) return;

    const scrollToCurrentTime = () => {
      const now = new Date();
      if (currentDate.toDateString() !== now.toDateString()) return;

      const timeSlotIndex = timeSlots.findIndex(slot => {
        if (['15min', '30min', '1hr'].includes(viewMode)) {
          if (viewMode === '15min' || viewMode === '30min') {
            return slot.hour === now.getHours() &&
                   (slot.minute || 0) <= now.getMinutes() &&
                   (slot.minute || 0) + (viewMode === '15min' ? 15 : 30) > now.getMinutes();
          } else {
            return slot.hour === now.getHours();
          }
        } else {
          return slot.isToday;
        }
      });

      if (timeSlotIndex >= 0 && timelineRef.current) {
        const slotWidth = viewMode === '15min' || viewMode === '30min' ? 200 :
                         viewMode === '1hr' ? 250 :
                         viewMode === '4hr' ? 300 :
                         viewMode === '8hr' ? 350 : 280;
        // Position current time at the beginning (left side) of the visible area
        const scrollPosition = timeSlotIndex * slotWidth;
        timelineRef.current.scrollTo({
          left: Math.max(0, scrollPosition),
          behavior: 'smooth'
        });
      }
    };

    scrollToCurrentTime();
    const interval = setInterval(scrollToCurrentTime, 60000);
    return () => clearInterval(interval);
  }, [timeSlots, viewMode, currentDate, autoFollow]);

  // Filter tasks
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      if (selectedProject !== 'all' && task.projectId !== selectedProject) {
        return false;
      }
      if (selectedTag !== 'all' && task.tagId !== selectedTag) {
        return false;
      }
      return true;
    });
  }, [tasks, selectedProject, selectedTag]);

  // Get tasks for specific slot
  const getTasksForSlot = (slot: any) => {
    return filteredTasks.filter(task => {
      if (!task.scheduledTime) return false;
      if (!['TODO', 'IN_PROGRESS'].includes(task.status)) return false;

      const taskDate = new Date(task.scheduledTime);

      switch (viewMode) {
        case '15min':
        case '30min':
          return taskDate.getHours() === slot.hour &&
                 taskDate.getMinutes() === (slot.minute || 0) &&
                 taskDate.toDateString() === slot.date.toDateString();

        case '1hr':
          return taskDate.getHours() === slot.hour &&
                 taskDate.toDateString() === slot.date.toDateString();

        case '4hr':
        case '8hr':
          const slotStart = slot.hour;
          const slotEnd = slot.hour + (slot.duration || 1);
          return taskDate.getHours() >= slotStart &&
                 taskDate.getHours() < slotEnd &&
                 taskDate.toDateString() === slot.date.toDateString();

        case 'daily':
        default:
          return taskDate.toDateString() === slot.date.toDateString();
      }
    });
  };

  // Categorize tasks by status for sidebar
  const unscheduledTasks = filteredTasks.filter(task =>
    !task.scheduledTime && task.status === 'TODO'
  );

  const pausedTasks = filteredTasks.filter(task =>
    task.status === 'PAUSED'
  );

  const completedTasks = filteredTasks.filter(task =>
    task.status === 'DONE'
  );

  const archivedTasks = filteredTasks.filter(task =>
    task.status === 'ARCHIVED'
  );

  // Perfect drag start handler
  const handleDragStart = (event: DragStartEvent) => {
    const task = tasks.find(t => t.id === event.active.id);
    setDraggedTask(task || null);
  };

  // Flawless drag end handler with perfect date handling
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedTask(null);

    if (!over) return;

    const taskId = active.id as string;
    const dropZoneId = over.id as string;

    try {
      // Handle section drops - remove from timeline and update status
      if (dropZoneId === 'unscheduled-section') {
        await updateTask(taskId, {
          status: 'TODO',
          scheduledTime: undefined,
        });
        return;
      }

      if (dropZoneId === 'paused-section') {
        await updateTask(taskId, {
          status: 'PAUSED',
          scheduledTime: undefined,
        });
        return;
      }

      if (dropZoneId === 'completed-section') {
        await updateTask(taskId, {
          status: 'DONE',
          scheduledTime: undefined,
        });
        return;
      }

      if (dropZoneId === 'archived-section') {
        await updateTask(taskId, {
          status: 'ARCHIVED',
          scheduledTime: undefined,
        });
        return;
      }

      // Handle timeline slot drops with PERFECT date handling
      let scheduledTime: Date;

      // Find the corresponding slot to get the exact date and time
      const targetSlot = timeSlots.find(slot => slot.id === dropZoneId);
      if (!targetSlot) {
        console.error('Target slot not found:', dropZoneId);
        return;
      }

      // Use the slot's date object directly for perfect accuracy
      scheduledTime = new Date(targetSlot.date);

      // For time-based views, set the exact hour and minute
      if (['15min', '30min', '1hr'].includes(viewMode)) {
        scheduledTime.setHours(targetSlot.hour || 0);
        scheduledTime.setMinutes(targetSlot.minute || 0);
        scheduledTime.setSeconds(0);
        scheduledTime.setMilliseconds(0);
      } else if (['4hr', '8hr'].includes(viewMode)) {
        // For multi-hour views, use the slot's starting hour
        scheduledTime.setHours(targetSlot.hour || 0);
        scheduledTime.setMinutes(0);
        scheduledTime.setSeconds(0);
        scheduledTime.setMilliseconds(0);
      } else {
        // For daily views, set to 9 AM
        scheduledTime.setHours(9, 0, 0, 0);
      }

      await updateTask(taskId, {
        status: 'IN_PROGRESS',
        scheduledTime: scheduledTime.toISOString(),
      });
    } catch (error) {
      console.error('Failed to update task:', error);
    }
  };

  const navigateTime = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (['15min', '30min', '1hr', '4hr', '8hr'].includes(viewMode)) {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    } else {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    }
    onDateChange(newDate);
  };

  // Proper text and spacing zoom functionality with position focus
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();

    if (!timelineRef.current) return;

    // Get mouse position relative to timeline
    const rect = timelineRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const scrollLeft = timelineRef.current.scrollLeft;

    // Calculate the content position under the mouse
    const contentX = scrollLeft + mouseX;

    // Text zoom speed
    const zoomSpeed = 0.05;
    const delta = -e.deltaY; // Invert for natural zoom direction

    setTextZoom(prevZoom => {
      const newZoom = Math.max(0.8, Math.min(1.5, prevZoom + (delta > 0 ? zoomSpeed : -zoomSpeed)));

      // Maintain focus on the mouse position
      if (timelineRef.current) {
        const zoomRatio = newZoom / prevZoom;
        const newContentX = contentX * zoomRatio;
        const newScrollLeft = newContentX - mouseX;

        // Apply the new scroll position after a brief delay to allow zoom to apply
        setTimeout(() => {
          if (timelineRef.current) {
            timelineRef.current.scrollLeft = Math.max(0, newScrollLeft);
          }
        }, 10);
      }

      return newZoom;
    });
  };

  // Pan event handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    // Only start panning if not clicking on a draggable task
    if ((e.target as HTMLElement).closest('[data-rbd-draggable-id], [data-rbd-droppable-id]')) {
      return;
    }

    if (!timelineRef.current) return;

    setIsPanning(true);
    setPanStart({
      x: e.clientX,
      scrollLeft: timelineRef.current.scrollLeft,
    });

    // Prevent text selection during pan
    e.preventDefault();
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isPanning || !timelineRef.current) return;

    e.preventDefault();
    const deltaX = e.clientX - panStart.x;
    timelineRef.current.scrollLeft = panStart.scrollLeft - deltaX;
  };

  const handleMouseUp = () => {
    setIsPanning(false);
  };

  const handleMouseLeave = () => {
    setIsPanning(false);
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="h-full flex flex-col bg-gradient-to-br from-gray-50 to-white">
        {/* Beautiful Timeline Header */}
        <div className="flex-shrink-0 p-6 border-b border-gray-200 bg-white shadow-sm">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Calendar className="h-6 w-6 mr-3 text-blue-600" />
              Timeline
            </h1>

            {/* Beautiful View Mode Toggle */}
            <div className="flex items-center space-x-2 bg-gray-100 rounded-xl p-1">
              {(['15min', '30min', '1hr', '4hr', '8hr', 'daily'] as ViewMode[]).map((mode) => (
                <Button
                  key={mode}
                  variant={viewMode === mode ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode(mode)}
                  className={`
                    transition-all duration-200 rounded-lg px-4 py-2
                    ${viewMode === mode
                      ? 'bg-white shadow-sm text-blue-600 font-semibold'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                    }
                  `}
                >
                  {mode}
                </Button>
              ))}
            </div>
          </div>

          {/* Navigation and Filters */}
          <div className="flex items-center justify-between">
            {/* Beautiful Date Navigation */}
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateTime('prev')}
                className="hover:bg-gray-100 rounded-lg p-2"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>

              <div className="text-center">
                <h2 className="text-lg font-semibold text-gray-900">
                  {['15min', '30min', '1hr', '4hr', '8hr'].includes(viewMode)
                    ? currentDate.toLocaleDateString('en-US', {
                        weekday: 'long',
                        month: 'long',
                        day: 'numeric',
                        year: 'numeric'
                      })
                    : `Week of ${timeSlots[0]?.label} - ${timeSlots[6]?.label}`
                  }
                </h2>
                <p className="text-sm text-gray-500">
                  {timeSlots.length} time slots
                </p>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateTime('next')}
                className="hover:bg-gray-100 rounded-lg p-2"
              >
                <ChevronRight className="h-5 w-5" />
              </Button>
            </div>

            {/* Beautiful Filters */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <select
                  value={selectedProject}
                  onChange={(e) => setSelectedProject(e.target.value)}
                  className="text-sm border border-gray-300 rounded-lg px-3 py-2 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Projects</option>
                  {projects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
              </div>

              <select
                value={selectedTag}
                onChange={(e) => setSelectedTag(e.target.value)}
                className="text-sm border border-gray-300 rounded-lg px-3 py-2 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Tags</option>
                {tags.map((tag) => (
                  <option key={tag.id} value={tag.id}>
                    {tag.name}
                  </option>
                ))}
              </select>

              <Button
                variant={autoFollow ? 'default' : 'outline'}
                size="sm"
                onClick={() => setAutoFollow(!autoFollow)}
                className="flex items-center space-x-2"
              >
                <Clock className="h-4 w-4" />
                <span>Follow</span>
              </Button>

              {/* Text Size Controls */}
              <div className="flex items-center space-x-2 border-l border-gray-300 pl-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setTextZoom(prev => Math.max(0.8, prev - 0.1))}
                  disabled={textZoom <= 0.8}
                  className="p-2"
                  title="Smaller Text"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>

                <div className="text-sm font-medium text-gray-700 min-w-[60px] text-center">
                  {Math.round(textZoom * 100)}%
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setTextZoom(prev => Math.min(1.5, prev + 0.1))}
                  disabled={textZoom >= 1.5}
                  className="p-2"
                  title="Larger Text"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setTextZoom(1)}
                  className="text-xs px-2"
                  title="Reset Text Size"
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Beautiful Timeline Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Beautiful Timeline Grid */}
          <div
            ref={timelineRef}
            className={`flex-1 overflow-x-auto overflow-y-hidden bg-gradient-to-b from-white to-gray-50 ${isPanning ? 'cursor-grabbing' : 'cursor-grab'}`}
            onScroll={() => setAutoFollow(false)}
            onWheel={handleWheel}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseLeave}
            style={{
              height: 'calc(100vh - 500px)', // Further reduced height for larger horizontal sections
              userSelect: isPanning ? 'none' : 'auto',
            }}
          >
            <div className="flex h-full min-w-max">
              {timeSlots.map((slot) => (
                <TimelineSlot
                  key={slot.id}
                  slot={slot}
                  tasks={getTasksForSlot(slot)}
                  viewMode={viewMode}
                  textZoom={textZoom}
                />
              ))}
            </div>
          </div>

          {/* Beautiful Horizontal Task Sections - Double Height */}
          <div className="h-96 border-t border-gray-200 bg-gradient-to-b from-gray-50 to-white p-4 overflow-y-auto">
            <div className="grid grid-cols-4 gap-4 h-full">
              <CollapsibleTaskSection
                id="unscheduled-section"
                title="Unscheduled Tasks"
                icon={<Clock className="h-4 w-4" />}
                tasks={unscheduledTasks}
                color="blue"
                description="Tasks waiting to be scheduled"
                defaultExpanded={true}
              />

              <CollapsibleTaskSection
                id="paused-section"
                title="Paused Tasks"
                icon={<Pause className="h-4 w-4" />}
                tasks={pausedTasks}
                color="yellow"
                description="Tasks temporarily paused"
                defaultExpanded={false}
              />

              <CollapsibleTaskSection
                id="completed-section"
                title="Completed Tasks"
                icon={<CheckCircle2 className="h-4 w-4" />}
                tasks={completedTasks}
                color="green"
                description="Finished tasks"
                defaultExpanded={false}
              />

              <CollapsibleTaskSection
                id="archived-section"
                title="Archived Tasks"
                icon={<Archive className="h-4 w-4" />}
                tasks={archivedTasks}
                color="gray"
                description="Archived tasks"
                defaultExpanded={false}
              />
            </div>
          </div>
        </div>

        {/* Beautiful Drag Overlay */}
        <DragOverlay>
          {draggedTask ? (
            <div className="transform rotate-3 scale-105">
              <TimelineTask task={draggedTask} textZoom={textZoom} />
            </div>
          ) : null}
        </DragOverlay>
      </div>
    </DndContext>
  );
};

export default HorizontalTimeline;