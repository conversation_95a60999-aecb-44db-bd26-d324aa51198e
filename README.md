# SimpleLife: ADHD-Friendly Task Management System

## Project Overview

SimpleLife is a comprehensive task management web application specifically designed for users with ADHD and executive function challenges. It provides an intuitive, drag-and-drop friendly interface that prioritizes visual time management through horizontal timelines, user-defined tags, and seamless task organization. The core philosophy is "Timeline First, Visual Organization, Zero Friction Capture."

## Problem Statement

Many individuals, particularly those with ADHD, struggle with:
1.  **Time Management & Visual Scheduling:** Difficulty understanding time allocation and task duration.
2.  **Working Memory & Idea Capture:** Random, important thoughts arise and are quickly forgotten if not captured immediately.
3.  **Flexible Organization:** Traditional priority systems are too rigid; users need personalized categorization.
4.  **Overwhelm & Complexity:** Traditional systems become cluttered and hard to navigate, leading to avoidance.

SimpleLife addresses these by providing a visual, timeline-focused system with user-defined tags, instant capture, and 100% drag-and-drop functionality.

## Key Features (Current Implementation)

*   **✅ Universal Quick Add:** Instantly capture any thought without breaking flow (Ctrl+Shift+A)
*   **✅ Dedicated Inbox:** A processing station to clarify and assign captured thoughts
*   **✅ Project-Based Organization:** Group tasks into meaningful contexts with custom colors
*   **✅ User-Defined Tags:** Replace rigid priorities with flexible, icon-based tags per project
*   **✅ Horizontal Timeline:** Primary view for visual time management with drag-and-drop scheduling
*   **✅ Task Zones:** Drag tasks between Not Scheduled, Paused, In Progress, and Completed zones
*   **✅ Current Task Footer:** Shows active task across all pages during work sessions
*   **✅ ADHD-Friendly UI:** Calm design, minimal clutter, visual indicators, smooth animations
*   **✅ Real-time Synchronization:** All views stay synchronized with immediate updates

## Target Audience

*   **Primary:** Individuals with ADHD seeking visual time management tools
*   **Secondary:** Students, professionals, freelancers overwhelmed by traditional task managers
*   **Tertiary:** Anyone who prefers visual, drag-and-drop interfaces over text-heavy systems

## Technology Stack (Implemented)

*   **Frontend:** React 18, TypeScript, Vite, Tailwind CSS, Zustand
*   **Backend:** Node.js, Express, TypeScript, Prisma ORM
*   **Database:** PostgreSQL with comprehensive schema
*   **Authentication:** JWT with secure session management
*   **Drag & Drop:** @dnd-kit for robust, accessible interactions

## Project Status

*   [x] Planning & Design
*   [x] MVP Development
*   [x] Core Features Implementation
*   [x] ADHD-Friendly Optimizations
*   [ ] Alpha/Beta Testing
*   [ ] Release
*   [ ] Post-Release Iteration

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or higher)
- PostgreSQL database
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd SimpleLife
   ```

2. **Install dependencies**
   ```bash
   npm run install:all
   ```

3. **Set up environment variables**

   **Backend (.env):**
   ```bash
   cp backend/.env.example backend/.env
   # Edit backend/.env with your database credentials
   ```

   **Frontend (.env):**
   ```bash
   cp frontend/.env.example frontend/.env
   # Edit frontend/.env if needed (defaults should work for development)
   ```

4. **Set up the database**
   ```bash
   cd backend
   npx prisma migrate dev --name init
   npx prisma generate
   ```

5. **Start the development servers**
   ```bash
   # From the root directory
   npm run dev
   ```

   This will start both the backend (port 3001) and frontend (port 5173) servers.

### 🌐 Access the Application

- **Frontend:** http://localhost:5173
- **Backend API:** http://localhost:3001
- **API Health Check:** http://localhost:3001/health

## 📱 Features

### Core Features (Implemented)
- ✅ **Universal Quick Add**: Instantly capture thoughts without breaking flow (`Ctrl+Shift+A`)
- ✅ **Dedicated Inbox**: Process and organize captured items with drag-and-drop
- ✅ **Project-Based Organization**: Group tasks into meaningful contexts with custom colors
- ✅ **User-Defined Tags**: Flexible categorization with custom icons and colors per project
- ✅ **Horizontal Timeline**: Primary view for visual time management and scheduling
- ✅ **Task Zones**: Drag tasks between Not Scheduled, Paused, In Progress, and Completed
- ✅ **Current Task Footer**: Real-time display of active tasks across all pages
- ✅ **User Authentication**: Secure account management with JWT
- ✅ **ADHD-Friendly UI**: Calm design optimized for focus and reduced overwhelm

### Key Highlights
- **Visual Time Management**: Horizontal timeline as the primary interface
- **100% Drag-and-Drop**: All task management through intuitive drag operations
- **Smart Processing**: Convert inbox items to organized tasks with project and tag assignment
- **Flexible Tagging**: User-defined tags replace rigid priority systems
- **Enhanced Filtering**: Filter tasks by Project and Tags across all views
- **Centralized Task Creation**: All new tasks created through Inbox for ADHD-friendly simplicity
- **Simplified Structure**: No complex subtasks - focus on actionable items
- **Real-time Updates**: All views synchronized with immediate feedback
- **Responsive Design**: Optimized for desktop with mobile-friendly interface

## 🏗️ Architecture

### Backend
- **Framework**: Node.js + Express + TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT-based secure authentication
- **API**: RESTful API with comprehensive error handling

### Frontend
- **Framework**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS with custom USER-friendly theme
- **State Management**: Zustand for predictable state updates
- **Routing**: React Router for seamless navigation
- **Forms**: React Hook Form with Zod validation

### Database Schema
- **Users**: Account management and authentication
- **Projects**: Organizational contexts with custom colors
- **Tags**: User-defined categorization with icons and colors (per project)
- **Tasks**: Simplified task entities with tag assignments (no subtasks)
- **InboxItems**: Temporary storage for quick captures

## 🧪 Development

### Available Scripts

**Root Level:**
```bash
npm run dev          # Start both frontend and backend
npm run build        # Build both applications
npm run install:all  # Install all dependencies
```

**Backend:**
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run db:migrate   # Run database migrations
npm run db:studio    # Open Prisma Studio
```

**Frontend:**
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
```

### Database Management

```bash
# Create and apply migrations
npx prisma migrate dev --name migration-name

# Reset database (development only)
npx prisma migrate reset

# Generate Prisma client
npx prisma generate

# Open Prisma Studio
npx prisma studio
```

## 🚀 Deployment

### Environment Setup
1. Set up PostgreSQL database (Supabase, Neon, Railway, etc.)
2. Configure environment variables for production
3. Run database migrations: `npx prisma migrate deploy`

### Recommended Platforms
- **Frontend**: Vercel, Netlify
- **Backend**: Render, Fly.io, Railway
- **Database**: Supabase, Neon, Railway

## 📋 Project Status

- [x] **Phase 0**: Foundation & Setup
- [x] **Phase 1**: Backend Core - User Authentication & Database Schema
- [x] **Phase 2**: Frontend Core - Authentication UI & State Management
- [x] **Phase 3**: Core Feature - Quick Add & Inbox Processing
- [x] **Phase 4**: Core Feature - Projects & Task Management
- [ ] **Phase 5**: Tag System - User-Defined Tags Replace Priorities
- [ ] **Phase 6**: Timeline Implementation - Horizontal Timeline & Task Zones
- [ ] **Phase 7**: ADHD Optimizations - Current Task Footer & Enhanced UX
- [ ] **Phase 8**: UI/UX Polish - Project Page Improvements & Kanban Views
- [ ] **Phase 9**: Advanced Features - Timeline Enhancements & Dashboard Restructure
- [ ] **Phase 10**: Deployment & Production Optimization

## 🎯 Current Implementation Status

### ✅ Completed (Core MVP)
- **Authentication System**: Secure user registration and login
- **Database Schema**: Complete with Users, Projects, Tags, Tasks, InboxItems
- **Tag System**: User-defined tags with icons and colors per project
- **Timeline Interface**: Horizontal timeline with drag-and-drop scheduling
- **Task Zones**: Not Scheduled, Paused, In Progress, Completed zones
- **Current Task Footer**: Real-time active task display across all pages
- **Inbox Processing**: Drag-and-drop conversion from thoughts to organized tasks

### 🔧 In Progress (Based on READ.md & PLAN.md)
- **Project Page Enhancements**: Tag count display and management interface
- **Kanban Project Views**: Better visual layout for individual project pages
- **Timeline Improvements**: Automatic task movement and enhanced time tracking
- **Dashboard Restructure**: Separate timeline and dashboard views with tabs

## 📚 Documentation

- [Current Implementation Status](./READ.md) - Detailed feature status and remaining work
- [Implementation Plan](./PLAN.md) - Complete roadmap for remaining features
- [Project Summary](./PROJECT_SUMMARY.md) - Technical implementation overview
- [Database Schema](./DATABASE.md) - Complete database design documentation
- [Implementation Status](./IMPLEMENTATION_STATUS.md) - Detailed progress tracking

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines (coming soon) for details on how to:
- Report bugs
- Suggest features
- Submit pull requests
- Follow our coding standards

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 💬 Support

- 📧 Email: [Your Contact Email]
- 🐛 Issues: [GitHub Issues](link-to-issues)
- 💡 Discussions: [GitHub Discussions](link-to-discussions)